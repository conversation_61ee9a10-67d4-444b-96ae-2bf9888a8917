import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  Space,
  Table,
  message,
  Tabs,
  InputNumber,
  TimePicker,
} from "antd";
import type { TabsProps } from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import type {
  TaskBasic,
  TaskAlert,
  DBConnection,
  AlertSend,
  OtherInfo,
} from "../types/task";
import { WEEKDAY_OPTIONS } from "../types/task";
import { TaskService } from "../services/taskService";
import {
  AlertModal,
  DbConnectionModal,
  AlertSendModal,
} from "./TaskFormModals";
import { OtherInfoModal, SelectModal } from "./TaskFormModalsExtended";

const { Option } = Select;

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit: (data: TaskBasic) => Promise<void>;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
  isEdit?: boolean;
}

// 抽屉表单
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  onReset,
  loading = false,
  isEdit = false,
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState("basic");

  // 各种数据状态
  const [alerts, setAlerts] = useState<TaskAlert[]>([]);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 可选择的数据
  const [availableAlerts, setAvailableAlerts] = useState<TaskAlert[]>([]);
  const [availableDbConnections, setAvailableDbConnections] =
    useState<DBConnection>();
  const [availableAlertSends, setAvailableAlertSends] = useState<AlertSend[]>(
    []
  );
  const [availableOtherInfos, setAvailableOtherInfos] = useState<OtherInfo[]>(
    []
  );

  // Modal状态
  const [alertModal, setAlertModal] = useState({
    visible: false,
    editingIndex: -1,
  });
  const [alertSendModal, setAlertSendModal] = useState({
    visible: false,
    editingIndex: -1,
  });
  const [dbConnectionModal, setDbConnectionModal] = useState({
    visible: false,
  });
  const [otherInfoModal, setOtherInfoModal] = useState({ visible: false });
  const [selectModal, setSelectModal] = useState({
    visible: false,
    type: "" as "alert" | "alertSend" | "dbConnection" | "otherInfo",
  });

  // 加载可选择的数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const [alertsData, dbConnectionsData, alertSendsData, otherInfosData] =
          await Promise.all([
            TaskService.getAlerts(),
            TaskService.getDbConnections(initialData?.id || 0),
            TaskService.getAlertSends(),
            TaskService.getOtherInfos(),
          ]);

        setAvailableAlerts(alertsData);
        if (dbConnectionsData) {
          setAvailableDbConnections(dbConnectionsData);
        }

        setAvailableAlertSends(alertSendsData);
        setAvailableOtherInfos(otherInfosData);
      } catch (error) {
        message.error("加载数据失败:" + error);
      }
    };

    loadData();
  }, [initialData]);

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      // 处理执行频率和重试频率
      const taskExec = { ...initialData };

      // 处理执行频率 - 解析格式如 "40sec"、"5hour" 等
      if (taskExec.frequency) {
        const frequencyMatch =
          typeof taskExec.frequency === "string"
            ? taskExec.frequency.match(/^(\d+)(sec|min|hour|day)$/)
            : null;
        if (frequencyMatch) {
          const [, value, unit] = frequencyMatch;
          // 将单位转换为中文
          let unitChinese = "";
          switch (unit) {
            case "sec":
              unitChinese = "秒";
              break;
            case "min":
              unitChinese = "分";
              break;
            case "hour":
              unitChinese = "时";
              break;
            case "day":
              unitChinese = "日";
              break;
            default:
              unitChinese = "分";
          }
          taskExec.frequency = { value: parseInt(value), unit: unitChinese };
        }
      }

      // 处理重试频率 - 解析格式如 "40sec"、"5hour" 等
      if (taskExec.retry_frequency) {
        const retryFrequencyMatch =
          typeof taskExec.retry_frequency === "string"
            ? taskExec.retry_frequency.match(/^(\d+)(sec|min|hour)$/)
            : null;
        if (retryFrequencyMatch) {
          const [, value, unit] = retryFrequencyMatch;
          // 将单位转换为中文
          let unitChinese = "";
          switch (unit) {
            case "sec":
              unitChinese = "秒";
              break;
            case "min":
              unitChinese = "分钟";
              break;
            case "hour":
              unitChinese = "小时";
              break;
            default:
              unitChinese = "分钟";
          }
          taskExec.retry_frequency = {
            value: parseInt(value),
            unit: unitChinese,
          };
        }
      }

      form.setFieldsValue(taskExec);
      // setAlerts(initialData.task_alerts);
      // setAlertSends(initialData.alert_sends);
      // setDbConnection(initialData.db_connection);
      // setOtherInfo(initialData.other_info_id);
    } else {
      // 新增模式时清空表单
      form.resetFields();
      setAlerts([]);
      setAlertSends([]);
      setDbConnection(null);
      setOtherInfo(null);
    }
  }, [initialData, form]);

  // 提交表单
  const handleSubmit = async (values: TaskBasic) => {
    try {
      // 处理表单数据，转换回原始格式
      const processedValues = { ...values };

      // 处理执行频率 - 转换为 "40sec"、"5hour" 等格式
      if (values.frequency && typeof values.frequency === "object") {
        const { value, unit } = values.frequency as {
          value: number;
          unit: string;
        };
        // 将中文单位转换为英文
        let unitEnglish = "";
        switch (unit) {
          case "秒":
            unitEnglish = "sec";
            break;
          case "分":
            unitEnglish = "min";
            break;
          case "时":
            unitEnglish = "hour";
            break;
          case "日":
            unitEnglish = "day";
            break;
          default:
            unitEnglish = "min";
        }
        processedValues.frequency = `${value}${unitEnglish}`;
      }

      // 处理重试频率 - 转换为 "40sec"、"5hour" 等格式
      if (
        values.retry_frequency &&
        typeof values.retry_frequency === "object"
      ) {
        const { value, unit } = values.retry_frequency as {
          value: number;
          unit: string;
        };
        // 将中文单位转换为英文
        let unitEnglish = "";
        switch (unit) {
          case "秒":
            unitEnglish = "sec";
            break;
          case "分钟":
            unitEnglish = "min";
            break;
          case "小时":
            unitEnglish = "hour";
            break;
          default:
            unitEnglish = "min";
        }
        processedValues.retry_frequency = `${value}${unitEnglish}`;
      }

      // 添加关联数据
      // processedValues.task_alerts = alerts;
      // processedValues.alert_sends = alertSends;
      // processedValues.db_connection = dbConnection;
      // processedValues.other_info_id = otherInfo;

      await onSubmit(processedValues);
    } catch (error) {
      message.error("提交失败:" + error);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setAlerts([]);
    setAlertSends([]);
    setDbConnection(null);
    setOtherInfo(null);
    if (onReset) {
      onReset();
    }
  };

  // 取消操作
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // 添加告警
  const handleAddAlert = (alert: TaskAlert) => {
    if (alertModal.editingIndex >= 0) {
      const newAlerts = [...alerts];
      newAlerts[alertModal.editingIndex] = alert;
      setAlerts(newAlerts);
    } else {
      setAlerts([...alerts, { ...alert, id: Date.now() }]);
    }
    setAlertModal({ visible: false, editingIndex: -1 });
  };

  // 删除告警
  const handleDeleteAlert = (index: number) => {
    const newAlerts = alerts.filter((_, i) => i !== index);
    setAlerts(newAlerts);
  };

  // 添加告警发送
  const handleAddAlertSend = (alertSend: AlertSend) => {
    if (alertSendModal.editingIndex >= 0) {
      const newAlertSends = [...alertSends];
      newAlertSends[alertSendModal.editingIndex] = alertSend;
      setAlertSends(newAlertSends);
    } else {
      setAlertSends([...alertSends, { ...alertSend, id: Date.now() }]);
    }
    setAlertSendModal({ visible: false, editingIndex: -1 });
  };

  // 删除告警发送
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    setAlertSends(newAlertSends);
  };

  // 选择已有数据
  const handleSelectExisting = (type: string, selectedItems: any[]) => {
    switch (type) {
      case "alert":
        setAlerts([...alerts, ...selectedItems]);
        break;
      case "alertSend":
        setAlertSends([...alertSends, ...selectedItems]);
        break;
      case "dbConnection":
        if (selectedItems.length > 0) {
          setDbConnection(selectedItems[0]);
        }
        break;
      case "otherInfo":
        if (selectedItems.length > 0) {
          setOtherInfo(selectedItems[0]);
        }
        break;
    }
    setSelectModal({ visible: false, type: "" as any });
  };

  // 定义Tabs的items配置
  const tabItems: TabsProps["items"] = [
    {
      key: "basic",
      label: "基本信息",
      children: (
        <Card title="任务执行配置" className="mb-4">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="任务名称"
                name="name"
                rules={[{ required: true, message: "请输入任务名称" }]}
              >
                <Input placeholder="请输入任务名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="任务分组"
                name="group"
                rules={[{ required: true, message: "请输入任务分组" }]}
              >
                <Input placeholder="请输入任务分组" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="开始时间"
                name="start_time"
                rules={[{ required: true, message: "请选择开始时间" }]}
                getValueFromEvent={(time) =>
                  time ? time.format("HH:mm:ss") : ""
                }
                getValueProps={(value) => ({
                  value: value
                    ? typeof value === "string"
                      ? undefined
                      : value
                    : undefined,
                })}
              >
                <TimePicker
                  placeholder="请选择开始时间"
                  format="HH:mm:ss"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="结束时间"
                name="end_time"
                rules={[{ required: true, message: "请选择结束时间" }]}
                getValueFromEvent={(time) =>
                  time ? time.format("HH:mm:ss") : ""
                }
                getValueProps={(value) => ({
                  value: value
                    ? typeof value === "string"
                      ? undefined
                      : value
                    : undefined,
                })}
              >
                <TimePicker
                  placeholder="请选择结束时间"
                  format="HH:mm:ss"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true, message: "请选择状态" }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="enabled">启用</Option>
                  <Option value="disabled">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="星期"
                name="weekday"
                rules={[{ required: true, message: "请选择星期" }]}
              >
                <Select mode="multiple" placeholder="请选择星期" allowClear>
                  {WEEKDAY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="执行频率"
                rules={[{ required: true, message: "请输入执行频率" }]}
              >
                <Space.Compact style={{ width: "100%" }}>
                  <Form.Item
                    name={["frequency", "value"]}
                    noStyle
                    rules={[{ required: true, message: "请输入频率值" }]}
                  >
                    <InputNumber
                      placeholder="请输入频率值"
                      style={{ width: "70%" }}
                      min={1}
                    />
                  </Form.Item>
                  <Form.Item
                    name={["frequency", "unit"]}
                    noStyle
                    rules={[{ required: true, message: "请选择单位" }]}
                  >
                    <Select placeholder="单位" style={{ width: "30%" }}>
                      <Option value="秒">秒</Option>
                      <Option value="分">分</Option>
                      <Option value="时">时</Option>
                      <Option value="日">日</Option>
                    </Select>
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="重试次数"
                name="retry_num"
                rules={[{ required: true, message: "请输入重试次数" }]}
              >
                <Input placeholder="请输入重试次数" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="重试间隔"
                rules={[{ required: true, message: "请输入重试间隔" }]}
              >
                <Space.Compact style={{ width: "100%" }}>
                  <Form.Item
                    name={["retry_frequency", "value"]}
                    noStyle
                    rules={[{ required: true, message: "请输入间隔值" }]}
                  >
                    <InputNumber
                      placeholder="请输入间隔值"
                      style={{ width: "70%" }}
                      min={1}
                    />
                  </Form.Item>
                  <Form.Item
                    name={["retry_frequency", "unit"]}
                    noStyle
                    rules={[{ required: true, message: "请选择单位" }]}
                  >
                    <Select placeholder="单位" style={{ width: "30%" }}>
                      <Option value="秒">秒</Option>
                      <Option value="分钟">分钟</Option>
                      <Option value="小时">小时</Option>
                    </Select>
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      ),
    },
    {
      key: "alerts",
      label: "告警配置",
      children: (
        <Card
          title="告警规则"
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() =>
                  setAlertModal({ visible: true, editingIndex: -1 })
                }
              >
                新增告警
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() => setSelectModal({ visible: true, type: "alert" })}
              >
                选择已有
              </Button>
            </Space>
          }
        >
          <Table
            dataSource={alerts}
            rowKey="id"
            pagination={false}
            columns={[
              { title: "告警名称", dataIndex: "name", key: "name" },
              { title: "告警级别", dataIndex: "severity", key: "severity" },
              { title: "告警类型", dataIndex: "type", key: "type" },
              {
                title: "SQL语句",
                dataIndex: "sql",
                key: "sql",
                ellipsis: true,
              },
              {
                title: "操作",
                key: "action",
                render: (_, record, index) => (
                  <Space>
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() =>
                        setAlertModal({ visible: true, editingIndex: index })
                      }
                    >
                      编辑
                    </Button>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteAlert(index)}
                    >
                      删除
                    </Button>
                  </Space>
                ),
              },
            ]}
          />
        </Card>
      ),
    },
    {
      key: "database",
      label: "数据库连接",
      children: (
        <Card
          title="数据库连接配置"
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setDbConnectionModal({ visible: true })}
              >
                新增连接
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({ visible: true, type: "dbConnection" })
                }
              >
                选择已有
              </Button>
            </Space>
          }
        >
          {dbConnection ? (
            <div className="p-4 border border-gray-200 rounded">
              <Row gutter={16}>
                <Col span={8}>
                  <strong>连接名称：</strong>
                  {dbConnection.name}
                </Col>
                <Col span={8}>
                  <strong>数据库类型：</strong>
                  {dbConnection.db_type}
                </Col>
                <Col span={8}>
                  <strong>主机地址：</strong>
                  {dbConnection.host}:{dbConnection.port}
                </Col>
              </Row>
              <div className="mt-2">
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => setDbConnectionModal({ visible: true })}
                >
                  编辑
                </Button>
                <Button
                  type="link"
                  danger
                  onClick={() => setDbConnection(null)}
                >
                  删除
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              暂无数据库连接配置
            </div>
          )}
        </Card>
      ),
    },
    {
      key: "alertSend",
      label: "告警发送",
      children: (
        <Card
          title="告警发送配置"
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() =>
                  setAlertSendModal({ visible: true, editingIndex: -1 })
                }
              >
                新增发送方式
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({ visible: true, type: "alertSend" })
                }
              >
                选择已有
              </Button>
            </Space>
          }
        >
          <Table
            dataSource={alertSends}
            rowKey="id"
            pagination={false}
            columns={[
              { title: "发送名称", dataIndex: "name", key: "name" },
              { title: "发送类型", dataIndex: "type", key: "type" },
              { title: "主机地址", dataIndex: "host", key: "host" },
              { title: "Topic", dataIndex: "topic", key: "topic" },
              {
                title: "操作",
                key: "action",
                render: (_, record, index) => (
                  <Space>
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() =>
                        setAlertSendModal({
                          visible: true,
                          editingIndex: index,
                        })
                      }
                    >
                      编辑
                    </Button>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteAlertSend(index)}
                    >
                      删除
                    </Button>
                  </Space>
                ),
              },
            ]}
          />
        </Card>
      ),
    },
    {
      key: "otherInfo",
      label: "其他信息",
      children: (
        <Card
          title="其他信息配置"
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setOtherInfoModal({ visible: true })}
              >
                新增信息
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({ visible: true, type: "otherInfo" })
                }
              >
                选择已有
              </Button>
            </Space>
          }
        >
          {otherInfo ? (
            <div className="p-4 border border-gray-200 rounded">
              <Row gutter={16}>
                <Col span={8}>
                  <strong>信息名称：</strong>
                  {otherInfo.name}
                </Col>
                <Col span={8}>
                  <strong>业务系统：</strong>
                  {otherInfo.business}
                </Col>
                <Col span={8}>
                  <strong>主机名称：</strong>
                  {otherInfo.hostname}
                </Col>
              </Row>
              <Row gutter={16} className="mt-2">
                <Col span={12}>
                  <strong>英文名称：</strong>
                  {otherInfo.businessEn}
                </Col>
                <Col span={12}>
                  <strong>告警来源：</strong>
                  {otherInfo.location}
                </Col>
              </Row>
              <div className="mt-2">
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => setOtherInfoModal({ visible: true })}
                >
                  编辑
                </Button>
                <Button type="link" danger onClick={() => setOtherInfo(null)}>
                  删除
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              暂无其他信息配置
            </div>
          )}
        </Card>
      ),
    },
  ];

  // console.log(tabItems);

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-auto">
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
          />
        </Form>
      </div>

      {/* 底部操作栏 */}
      <div className="flex-shrink-0 border-t border-gray-200 bg-white p-4">
        <div className="flex justify-end space-x-3">
          {isEdit ? (
            // 编辑模式：取消/确认
            <>
              <Button onClick={handleCancel} className="rounded-md px-6">
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => form.submit()}
                loading={loading}
                className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md px-6"
              >
                确认
              </Button>
            </>
          ) : (
            // 新增模式：取消/重置/提交
            <>
              <Button onClick={handleCancel} className="rounded-md px-6">
                取消
              </Button>
              <Button onClick={handleReset} className="rounded-md px-6">
                重置
              </Button>
              <Button
                type="primary"
                onClick={() => form.submit()}
                loading={loading}
                className="bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 rounded-md px-6"
              >
                提交
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 各种Modal组件 */}
      <AlertModal
        visible={alertModal.visible}
        editingData={
          alertModal.editingIndex >= 0
            ? alerts[alertModal.editingIndex]
            : undefined
        }
        onCancel={() => setAlertModal({ visible: false, editingIndex: -1 })}
        onSubmit={handleAddAlert}
      />

      <DbConnectionModal
        visible={dbConnectionModal.visible}
        editingData={dbConnection || undefined}
        onCancel={() => setDbConnectionModal({ visible: false })}
        onSubmit={(data) => {
          setDbConnection(data);
          setDbConnectionModal({ visible: false });
        }}
      />

      <AlertSendModal
        visible={alertSendModal.visible}
        editingData={
          alertSendModal.editingIndex >= 0
            ? alertSends[alertSendModal.editingIndex]
            : undefined
        }
        onCancel={() => setAlertSendModal({ visible: false, editingIndex: -1 })}
        onSubmit={handleAddAlertSend}
      />

      <OtherInfoModal
        visible={otherInfoModal.visible}
        editingData={otherInfo || undefined}
        onCancel={() => setOtherInfoModal({ visible: false })}
        onSubmit={(data) => {
          setOtherInfo(data);
          setOtherInfoModal({ visible: false });
        }}
      />

      <SelectModal
        visible={selectModal.visible}
        type={selectModal.type}
        data={
          selectModal.type === "alert"
            ? availableAlerts
            : selectModal.type === "alertSend"
            ? availableAlertSends
            : selectModal.type === "dbConnection"
            ? availableDbConnections
            : selectModal.type === "otherInfo"
            ? availableOtherInfos
            : []
        }
        multiple={
          selectModal.type !== "dbConnection" &&
          selectModal.type !== "otherInfo"
        }
        onCancel={() => setSelectModal({ visible: false, type: "" as any })}
        onSubmit={(selectedItems) =>
          handleSelectExisting(selectModal.type, selectedItems)
        }
      />
    </div>
  );
};

export default ComplexTaskForm;
