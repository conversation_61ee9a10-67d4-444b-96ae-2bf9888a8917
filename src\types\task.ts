// 基本信息，页面加载时查询的数据
// 主页表格展示数据
// export type TableData = Omit<TaskBasic, 'id' | 'alert_task_id' | 'alert_send_id' | 'db_connection_id' | 'other_info_id'>
// 表格不展示 id alert_task_id alert_send_id other_info_id db_connection_id
export interface TaskBasic {
  // 主键
  id: number;
  // 任务名称：唯一
  name: string;
  // 任务分组：非多选
  group: string;
  /** 状态 */
  status: 'enabled' | 'disabled';
  // 开始时间, 格式为HH:mm:ss
  start_time: string;
  // 结束时间, 格式为HH:mm:ss
  end_time: string;
  // 星期，格式 1-7 / 1,2,3,4,5
  // 表格展示格式：1,2,3,4,5
  // 抽屉编辑：转换为中文星期几，提交时转换回数字格式
  weekday: string[];
  // 执行频率(间隔)： 
  // 表格展示格式：40sec/5hour
  // 抽屉编辑：数字：xxx, 单位：秒/分/时/日
  frequency: string | { value: number; unit: string };
  // 重试次数
  retry_num: string;
  // 重试间隔
  // 表格展示格式：40sec/5hour
  // 抽屉编辑：数字：xxx, 单位：秒/分钟/小时
  retry_frequency: string | { value: number; unit: string };
  // 告警触发条件
  alert_task_id: string[];
  // 告警发送方式
  alert_send_id: string[];
  // db 连接id
  db_connection_id: string;
  // 附加信息id
  other_info_id: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 复合表单数据类型 - 包含所有相关表的数据
// export interface ComplexFormData {
//   task_exec: Omit<TaskBasic, 'id'>;
//   task_alerts: TaskAlert[];
//   db_connection: DBConnection | null;
//   alert_sends: AlertSend[];
//   other_info: OtherInfo | null;
// }


// 基本信息表单，抽屉内，更新/删除需要id，表单不展示id
export type TaskBasicFormDataAdd = Omit<TaskBasic, 'id' | 'createTime' | 'updateTime'>
export type TaskBasicFormDataUpdateOrDelete = Omit<TaskBasic, 'createTime' | 'updateTime'>

// 告警配置，基本信息包含多个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息alert_task_id查询数据, table data, 页面不展示id
export interface TaskAlert {
  // 主键
  id: number;
  // 唯一
  name: string;
  // 告警级别
  severity: string;
  // 告警对象：执行SQL结果
  sql: string;
  // 告警类型: isExist/isEqual
  type: string
  // 触发值：isEqual时  针对SQL返回值判断; isExist时 values 为空
  values: string[];
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}


// 抽屉内 告警配置 表单数据
export type TaskAlertFormAdd = Omit<TaskAlert, 'id' | 'createTime'| 'updateTime'>
export type TaskAlertFormUpdateOrDelet = Omit<TaskAlert, 'createTime'| 'updateTime'>

// 数据库配置， 基本信息包含单个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息db_connection_id查询数据
export interface DBConnection {
  id: number;
  // 唯一
  name: string;
  // 数据库类型：mysql/oracle
  db_type: string;
  // 数据库连接信息
  host: string;
  // 端口
  port: string;
  // 用户名
  user: string;
  // base64 简单加密
  passwd: string;
  // mysql 独有
  database: string;
  use_ssl: boolean;
  server_timezone: string;
  // oracle 独有
  instance: string;
  // oracle 独有 sid/service
  connect_method: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 抽屉内 DB配置 表单数据
export type DBFormAdd = Omit<DBConnection, 'id' | 'createTime'| 'updateTime'>
export type DBFormUpdateOrDelet = Omit<DBConnection, 'createTime'| 'updateTime'>

// 告警发送配置， 基本信息包含多个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息alert_send_id查询数据
export interface AlertSend {
  id: number;
  // 唯一
  name: string;
  // 接收类型：kafka/prometheus
  type: string;
  // prometheus地址
  host: string;
  // kafka 独有
  topic: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 抽屉内 告警发送配置 表单数据
export type AlertSendFormAdd = Omit<DBConnection, 'id' | 'createTime'| 'updateTime'>
export type AlertSendFormUpdateOrDelet = Omit<DBConnection, 'createTime'| 'updateTime'>

// 附加信息配置， 基本信息包含单个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息other_info_id查询数据
export interface OtherInfo {
  id: number;
  // 唯一
  name: string;
  // 业务系统名称
  business: string;
  // 业务系统英文名称
  business_en: string;
  // 主机名称
  hostname: string;
  // 告警来源
  location: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 抽屉内 告警发送配置 表单数据
export type OtherFormAdd = Omit<DBConnection, 'id' | 'createTime'| 'updateTime'>
export type OtherFormUpdateOrDelet = Omit<DBConnection, 'createTime'| 'updateTime'>




/**
 * 任务数据项类型定义 - 使用task_exec作为主表数据
 * 用于定义表格中每一行任务数据的类型
 */
// export interface TaskData extends TaskBasic {
//   /** 创建时间 */
//   createTime: string;
// }

/**
 * 任务表单数据类型（不包含id和createTime）
 */
// export type TaskFormData = Omit<TaskData, 'id' | 'createTime'>;

/**
 * 搜索参数类型
 */
export type TaskSearchParams = {
  current?: number;
  pageSize?: number;
  name?: string;
  group?: string;
  status?: 'enabled' | 'disabled';
  weekday?: string;
  frequency?: string;
  start_time?: string;
  end_time?: string;
  retryNum?: string;
  retry_frequency?: string;
  db_type?: string;
};

/**
 * API响应类型
 */
export type TaskApiResponse<T> = {
  data: T[];
  total: number;
  success: boolean;
  message?: string;
};

/**
 * 表格选择状态类型
 */
export type TaskSelectionState = {
  selectedRowKeys: React.Key[];
  selectedRows: TaskBasic[];
};

/**
 * Modal状态类型
 */
export type TaskModalState = {
  visible: boolean;
  loading?: boolean;
};

/**
 * 抽屉状态类型
 */
export type TaskDrawerState = {
  visible: boolean;
  loading?: boolean;
};

/**
 * 表单操作类型
 */
export type TaskFormAction = 'add' | 'edit';

/**
 * 更新方式类型
 */
export type TaskUpdateMethod = 'reload' | 'direct';

/**
 * 任务状态选项
 */
export const TASK_STATUS_OPTIONS = [
  { label: '激活', value: 'active' },
  { label: '未激活', value: 'inactive' },
  { label: '待处理', value: 'pending' },
] as const;

/**
 * 星期选项
 */
export const WEEKDAY_OPTIONS = [
  { label: '周一', value: '1' },
  { label: '周二', value: '2' },
  { label: '周三', value: '3' },
  { label: '周四', value: '4' },
  { label: '周五', value: '5' },
  { label: '周六', value: '6' },
  { label: '周日', value: '7' },
] as const;

/**
 * 频率选项
 */
export const FREQUENCY_OPTIONS = [
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '自定义', value: 'custom' },
] as const;



/**
 * 数据库类型选项
 */
export const DB_TYPE_OPTIONS = [
  { label: 'MySQL', value: 'mysql' },
  { label: 'Oracle', value: 'oracle' },
] as const;

/**
 * 告警类型选项
 */
export const ALERT_TYPE_OPTIONS = [
  { label: '存在性检查', value: 'isExist' },
  { label: '相等性检查', value: 'isEqual' },
] as const;

/**
 * 告警级别选项
 */
export const ALERT_SEVERITY_OPTIONS = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'critical' },
] as const;

/**
 * 发送类型选项
 */
export const SEND_TYPE_OPTIONS = [
  { label: 'Kafka', value: 'kafka' },
  { label: 'Prometheus', value: 'prometheus' },
] as const;

/**
 * Oracle连接方式选项
 */
export const ORACLE_CONNECT_METHOD_OPTIONS = [
  { label: 'SID', value: 'sid' },
  { label: 'Service', value: 'service' },
] as const;
